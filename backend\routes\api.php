<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\CompanyController;
use App\Http\Controllers\Api\ProductController;
use App\Http\Controllers\Api\SaleController;
use App\Http\Controllers\Api\DashboardController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// المسارات العامة (بدون مصادقة)
Route::prefix('v1')->group(function () {
    // مسارات المصادقة
    Route::post('/login', [AuthController::class, 'login']);
    
    // مسارات محمية بالمصادقة
    Route::middleware('auth:sanctum')->group(function () {
        // مسارات المصادقة المحمية
        Route::post('/logout', [AuthController::class, 'logout']);
        Route::get('/me', [AuthController::class, 'me']);
        Route::put('/password', [AuthController::class, 'updatePassword']);
        
        // لوحة التحكم
        Route::get('/dashboard/stats', [DashboardController::class, 'getStats']);
        Route::get('/dashboard/recent-sales', [DashboardController::class, 'getRecentSales']);
        Route::get('/dashboard/top-products', [DashboardController::class, 'getTopProducts']);
        Route::get('/dashboard/sales-chart', [DashboardController::class, 'getSalesChart']);
        
        // إدارة الشركات (للمشرفين فقط)
        Route::middleware('role:admin,supervisor')->group(function () {
            Route::apiResource('companies', CompanyController::class);
        });
        
        // إدارة المنتجات
        Route::apiResource('products', ProductController::class);
        Route::get('/products/search/{term}', [ProductController::class, 'search']);
        Route::get('/products/barcode/{barcode}', [ProductController::class, 'findByBarcode']);
        Route::post('/products/bulk-import', [ProductController::class, 'bulkImport']);
        Route::get('/products/low-stock', [ProductController::class, 'getLowStock']);
        
        // إدارة المبيعات
        Route::apiResource('sales', SaleController::class);
        Route::post('/sales/{sale}/refund', [SaleController::class, 'refund']);
        Route::get('/sales/{sale}/receipt', [SaleController::class, 'getReceipt']);
        Route::post('/sales/pos', [SaleController::class, 'createPOSSale']);
        
        // التقارير
        Route::prefix('reports')->group(function () {
            Route::get('/sales', [SaleController::class, 'getSalesReport']);
            Route::get('/products', [ProductController::class, 'getProductsReport']);
            Route::get('/inventory', [ProductController::class, 'getInventoryReport']);
        });
    });
});
