import React, { useEffect, useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useLanguage } from '@/contexts/LanguageContext';
import Layout from '@/components/layout/Layout';
import Login from './Login';
import Dashboard from './Dashboard';
import POS from './POS';

const Index = () => {
  const { isAuthenticated, isLoading } = useAuth();
  const { t } = useLanguage();
  const [currentPage, setCurrentPage] = useState('/dashboard');

  // إعادة توجيه تلقائي حسب حالة المصادقة
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      setCurrentPage('/login');
    } else if (!isLoading && isAuthenticated) {
      setCurrentPage('/dashboard');
    }
  }, [isAuthenticated, isLoading]);

  // معالج التنقل
  const handleNavigate = (path: string) => {
    setCurrentPage(path);
  };

  // إذا كان النظام لا يزال يحمل
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-muted-foreground font-arabic">جاري التحميل...</p>
        </div>
      </div>
    );
  }

  // إذا لم يكن المستخدم مسجلاً دخولاً، عرض صفحة تسجيل الدخول
  if (!isAuthenticated) {
    return <Login />;
  }

  // تحديد المحتوى بناءً على الصفحة الحالية
  const renderCurrentPage = () => {
    switch (currentPage) {
      case '/dashboard':
        return <Dashboard />;
      case '/pos':
        return <POS />;
      default:
        return <Dashboard />;
    }
  };

  // تحديد عنوان الصفحة
  const getPageTitle = () => {
    switch (currentPage) {
      case '/dashboard':
        return t('nav.dashboard');
      case '/pos':
        return t('nav.pos');
      default:
        return t('nav.dashboard');
    }
  };

  return (
    <Layout
      title={getPageTitle()}
      currentPath={currentPage}
      onNavigate={handleNavigate}
    >
      {renderCurrentPage()}
    </Layout>
  );
};

export default Index;
