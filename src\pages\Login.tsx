import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useAuth } from '@/contexts/AuthContext';
import { useLanguage } from '@/contexts/LanguageContext';
import { Store, Mail, Lock, Loader2, Eye, EyeOff } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

const Login: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  
  const { login } = useAuth();
  const { t } = useLanguage();
  const { toast } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email || !password) {
      toast({
        title: "خطأ",
        description: "يرجى إدخال البريد الإلكتروني وكلمة المرور",
        variant: "destructive"
      });
      return;
    }

    setIsLoading(true);
    try {
      await login(email, password);
      toast({
        title: "مرحباً بك",
        description: "تم تسجيل الدخول بنجاح",
      });
    } catch (error) {
      toast({
        title: "خطأ في تسجيل الدخول",
        description: "البريد الإلكتروني أو كلمة المرور غير صحيحة",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  // حسابات تجريبية للاختبار
  const demoAccounts = [
    { email: '<EMAIL>', password: '123456', role: 'مدير عام' },
    { email: '<EMAIL>', password: '123456', role: 'مالك المتجر' },
    { email: '<EMAIL>', password: '123456', role: 'كاشير' }
  ];

  const fillDemoAccount = (account: typeof demoAccounts[0]) => {
    setEmail(account.email);
    setPassword(account.password);
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-primary/5 via-background to-accent/5 p-4">
      {/* الخلفية المتحركة */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-primary/10 rounded-full blur-3xl animate-bounce-subtle"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-accent/10 rounded-full blur-3xl animate-bounce-subtle" style={{ animationDelay: '1s' }}></div>
      </div>

      <div className="relative w-full max-w-md space-y-6">
        {/* شعار وعنوان النظام */}
        <div className="text-center space-y-4">
          <div className="mx-auto w-16 h-16 bg-gradient-primary rounded-2xl flex items-center justify-center shadow-primary">
            <Store className="w-8 h-8 text-primary-foreground" />
          </div>
          <div>
            <h1 className="text-3xl font-bold font-arabic bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
              {t('app.title')}
            </h1>
            <p className="text-muted-foreground font-arabic mt-2">
              {t('app.description')}
            </p>
          </div>
        </div>

        {/* نموذج تسجيل الدخول */}
        <Card className="card-elevated">
          <CardHeader className="space-y-1">
            <CardTitle className="text-2xl font-arabic">{t('login.title')}</CardTitle>
            <CardDescription className="font-arabic">
              أدخل بياناتك للوصول إلى نظام إدارة نقاط البيع
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              {/* حقل البريد الإلكتروني */}
              <div className="space-y-2">
                <Label htmlFor="email" className="font-arabic">{t('login.email')}</Label>
                <div className="relative">
                  <Mail className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                  <Input
                    id="email"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="input-enhanced pr-10"
                    placeholder="<EMAIL>"
                    required
                  />
                </div>
              </div>

              {/* حقل كلمة المرور */}
              <div className="space-y-2">
                <Label htmlFor="password" className="font-arabic">{t('login.password')}</Label>
                <div className="relative">
                  <Lock className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                  <Input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="input-enhanced pr-10 pl-10"
                    placeholder="••••••"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
                  >
                    {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                  </button>
                </div>
              </div>

              {/* زر تسجيل الدخول */}
              <Button
                type="submit"
                className="w-full btn-gradient-primary h-11 font-arabic"
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    {t('login.loading')}
                  </>
                ) : (
                  t('login.submit')
                )}
              </Button>

              {/* رابط نسيان كلمة المرور */}
              <div className="text-center">
                <button
                  type="button"
                  className="text-sm text-primary hover:text-primary-glow transition-colors font-arabic"
                >
                  {t('login.forgot')}
                </button>
              </div>
            </form>
          </CardContent>
        </Card>

        {/* الحسابات التجريبية */}
        <Card className="card-elevated">
          <CardHeader>
            <CardTitle className="text-lg font-arabic">حسابات تجريبية</CardTitle>
            <CardDescription className="font-arabic">
              انقر على أي حساب لملء البيانات تلقائياً
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {demoAccounts.map((account, index) => (
                <button
                  key={index}
                  type="button"
                  onClick={() => fillDemoAccount(account)}
                  className="w-full p-3 text-right bg-muted/50 hover:bg-muted rounded-lg transition-colors"
                >
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground font-arabic">
                      {account.role}
                    </span>
                    <div className="text-sm font-medium font-english">
                      {account.email}
                    </div>
                  </div>
                </button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* معلومات إضافية */}
        <div className="text-center text-sm text-muted-foreground font-arabic">
          <p>نظام نقاط البيع المتكامل - إصدار 2024</p>
          <p>جميع الحقوق محفوظة</p>
        </div>
      </div>
    </div>
  );
};

export default Login;