import React, { useState } from 'react';
import { 
  Package, 
  Plus, 
  Search, 
  Filter, 
  Download, 
  Upload,
  Edit,
  Trash2,
  AlertTriangle,
  TrendingDown
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { useLanguage } from '@/contexts/LanguageContext';

const Inventory: React.FC = () => {
  const { t, isRTL } = useLanguage();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  // بيانات تجريبية للمخزون
  const inventoryItems = [
    {
      id: '1',
      name: 'آيفون 15 برو',
      nameEn: 'iPhone 15 Pro',
      sku: 'IPH15P-256-BLU',
      category: 'electronics',
      stock: 25,
      minStock: 10,
      price: 4500,
      cost: 3800,
      supplier: 'أبل السعودية',
      lastUpdated: '2024-01-15',
      status: 'active'
    },
    {
      id: '2',
      name: 'قهوة عربية فاخرة',
      nameEn: 'Premium Arabic Coffee',
      sku: 'COF-ARB-500G',
      category: 'food',
      stock: 5,
      minStock: 20,
      price: 45,
      cost: 25,
      supplier: 'محمصة الذهب',
      lastUpdated: '2024-01-14',
      status: 'low_stock'
    },
    {
      id: '3',
      name: 'تي شيرت قطني',
      nameEn: 'Cotton T-Shirt',
      sku: 'TSH-COT-L-WHT',
      category: 'clothing',
      stock: 0,
      minStock: 15,
      price: 85,
      cost: 45,
      supplier: 'نسيج الأناقة',
      lastUpdated: '2024-01-13',
      status: 'out_of_stock'
    }
  ];

  const categories = [
    { id: 'all', name: 'جميع المنتجات' },
    { id: 'electronics', name: 'إلكترونيات' },
    { id: 'food', name: 'أطعمة ومشروبات' },
    { id: 'clothing', name: 'ملابس' },
    { id: 'beauty', name: 'تجميل وعناية' }
  ];

  // تصفية المنتجات
  const filteredItems = inventoryItems.filter(item => {
    const matchesSearch = 
      item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.nameEn.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.sku.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || item.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  // إحصائيات المخزون
  const totalItems = inventoryItems.length;
  const lowStockItems = inventoryItems.filter(item => item.stock <= item.minStock && item.stock > 0).length;
  const outOfStockItems = inventoryItems.filter(item => item.stock === 0).length;
  const totalValue = inventoryItems.reduce((sum, item) => sum + (item.stock * item.cost), 0);

  const getStatusBadge = (item: typeof inventoryItems[0]) => {
    if (item.stock === 0) {
      return <Badge variant="destructive" className="font-arabic">نفد المخزون</Badge>;
    } else if (item.stock <= item.minStock) {
      return <Badge variant="warning" className="font-arabic">مخزون منخفض</Badge>;
    }
    return <Badge variant="success" className="font-arabic">متوفر</Badge>;
  };

  return (
    <div className="space-y-6">
      {/* إحصائيات سريعة */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="card-elevated">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground font-arabic">
                  إجمالي المنتجات
                </p>
                <p className="text-2xl font-bold font-english">{totalItems}</p>
              </div>
              <Package className="w-8 h-8 text-primary" />
            </div>
          </CardContent>
        </Card>

        <Card className="card-elevated">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground font-arabic">
                  مخزون منخفض
                </p>
                <p className="text-2xl font-bold font-english text-warning">{lowStockItems}</p>
              </div>
              <AlertTriangle className="w-8 h-8 text-warning" />
            </div>
          </CardContent>
        </Card>

        <Card className="card-elevated">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground font-arabic">
                  نفد المخزون
                </p>
                <p className="text-2xl font-bold font-english text-destructive">{outOfStockItems}</p>
              </div>
              <TrendingDown className="w-8 h-8 text-destructive" />
            </div>
          </CardContent>
        </Card>

        <Card className="card-elevated">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground font-arabic">
                  قيمة المخزون
                </p>
                <p className="text-2xl font-bold font-english">
                  {totalValue.toLocaleString()} ر.س
                </p>
              </div>
              <Package className="w-8 h-8 text-success" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* أدوات التحكم */}
      <Card className="card-elevated">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="font-arabic">إدارة المخزون</CardTitle>
              <CardDescription className="font-arabic">
                إدارة وتتبع جميع منتجات المتجر
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm">
                <Download className="w-4 h-4 mr-2" />
                <span className="font-arabic">تصدير</span>
              </Button>
              <Button variant="outline" size="sm">
                <Upload className="w-4 h-4 mr-2" />
                <span className="font-arabic">استيراد</span>
              </Button>
              <Button size="sm" className="font-arabic">
                <Plus className="w-4 h-4 mr-2" />
                إضافة منتج
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* شريط البحث والتصفية */}
          <div className="flex flex-col md:flex-row gap-4">
            <div className="relative flex-1">
              <Search className={`absolute top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground ${isRTL ? 'right-3' : 'left-3'}`} />
              <Input
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="البحث عن منتج، SKU، أو اسم..."
                className={`input-enhanced ${isRTL ? 'pr-10' : 'pl-10'}`}
              />
            </div>
            <div className="flex gap-2">
              {categories.map((category) => (
                <Button
                  key={category.id}
                  variant={selectedCategory === category.id ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedCategory(category.id)}
                  className="font-arabic"
                >
                  {category.name}
                </Button>
              ))}
            </div>
          </div>

          {/* جدول المنتجات */}
          <div className="border rounded-lg overflow-hidden">
            <div className="bg-muted/50 p-4 border-b">
              <div className="grid grid-cols-12 gap-4 text-sm font-medium text-muted-foreground">
                <div className="col-span-3 font-arabic">المنتج</div>
                <div className="col-span-2 font-arabic">SKU</div>
                <div className="col-span-1 font-arabic">الكمية</div>
                <div className="col-span-2 font-arabic">السعر</div>
                <div className="col-span-2 font-arabic">الحالة</div>
                <div className="col-span-2 font-arabic">الإجراءات</div>
              </div>
            </div>
            <div className="divide-y">
              {filteredItems.map((item) => (
                <div key={item.id} className="p-4 hover:bg-muted/30 transition-colors">
                  <div className="grid grid-cols-12 gap-4 items-center">
                    <div className="col-span-3">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-muted rounded-lg flex items-center justify-center">
                          <Package className="w-5 h-5 text-muted-foreground" />
                        </div>
                        <div>
                          <p className="font-medium font-arabic">{item.name}</p>
                          <p className="text-sm text-muted-foreground font-arabic">
                            {item.supplier}
                          </p>
                        </div>
                      </div>
                    </div>
                    <div className="col-span-2">
                      <p className="font-mono text-sm">{item.sku}</p>
                    </div>
                    <div className="col-span-1">
                      <p className="font-bold font-english">
                        {item.stock}
                      </p>
                      <p className="text-xs text-muted-foreground font-arabic">
                        الحد الأدنى: {item.minStock}
                      </p>
                    </div>
                    <div className="col-span-2">
                      <p className="font-bold font-english">{item.price} ر.س</p>
                      <p className="text-xs text-muted-foreground font-english">
                        التكلفة: {item.cost} ر.س
                      </p>
                    </div>
                    <div className="col-span-2">
                      {getStatusBadge(item)}
                    </div>
                    <div className="col-span-2">
                      <div className="flex items-center gap-2">
                        <Button variant="ghost" size="sm">
                          <Edit className="w-4 h-4" />
                        </Button>
                        <Button variant="ghost" size="sm" className="text-destructive hover:text-destructive">
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {filteredItems.length === 0 && (
            <div className="text-center py-12">
              <Package className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground font-arabic">
                لا توجد منتجات تطابق البحث
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default Inventory;