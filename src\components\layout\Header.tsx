import React from 'react';
import { 
  Bell, 
  Globe, 
  Moon, 
  Sun, 
  Search,
  Calculator,
  TrendingUp
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { useLanguage } from '@/contexts/LanguageContext';
import { useAuth } from '@/contexts/AuthContext';
import { cn } from '@/lib/utils';

interface HeaderProps {
  title: string;
  isDarkMode: boolean;
  onToggleDarkMode: () => void;
}

const Header: React.FC<HeaderProps> = ({ title, isDarkMode, onToggleDarkMode }) => {
  const { language, setLanguage, t, isRTL } = useLanguage();
  const { company } = useAuth();

  // بيانات إحصائية سريعة للرأس
  const quickStats = [
    {
      label: t('dashboard.sales.today'),
      value: '12,450',
      currency: 'ر.س',
      trend: '+12%',
      positive: true
    },
    {
      label: 'الطلبات اليوم',
      value: '48',
      trend: '+8%',
      positive: true
    }
  ];

  return (
    <header className="h-16 bg-card border-b border-border shadow-soft px-6 flex items-center justify-between">
      {/* الجانب الأيمن/الأيسر - العنوان والبحث */}
      <div className="flex items-center gap-4 flex-1">
        <div>
          <h1 className="text-xl font-bold font-arabic">{title}</h1>
          {company && (
            <p className="text-sm text-muted-foreground">
              {company.name} - الاشتراك: {company.subscriptionPlan}
            </p>
          )}
        </div>
        
        {/* شريط البحث السريع */}
        <div className="relative max-w-md flex-1">
          <Search className={cn(
            "absolute top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground",
            isRTL ? "right-3" : "left-3"
          )} />
          <Input
            placeholder={t('pos.search')}
            className={cn(
              "input-enhanced",
              isRTL ? "pr-10" : "pl-10"
            )}
          />
        </div>
      </div>

      {/* الإحصائيات السريعة - للشاشات الكبيرة فقط */}
      <div className="hidden lg:flex items-center gap-6 mx-6">
        {quickStats.map((stat, index) => (
          <div key={index} className="text-center">
            <div className="flex items-center gap-2">
              <span className="text-2xl font-bold font-english">
                {stat.value}
              </span>
              {stat.currency && (
                <span className="text-sm text-muted-foreground">
                  {stat.currency}
                </span>
              )}
              <Badge
                variant={stat.positive ? "default" : "destructive"}
                className="text-xs"
              >
                <TrendingUp className="w-3 h-3 mr-1" />
                {stat.trend}
              </Badge>
            </div>
            <p className="text-xs text-muted-foreground font-arabic">
              {stat.label}
            </p>
          </div>
        ))}
      </div>

      {/* الجانب الأيسر/الأيمن - أدوات التحكم */}
      <div className="flex items-center gap-2">
        {/* زر الآلة الحاسبة السريعة */}
        <Button
          variant="ghost"
          size="sm"
          className="h-9 w-9 p-0"
          title="الآلة الحاسبة"
        >
          <Calculator className="w-4 h-4" />
        </Button>

        {/* زر تبديل اللغة */}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setLanguage(language === 'ar' ? 'en' : 'ar')}
          className="h-9 w-9 p-0"
          title="تغيير اللغة"
        >
          <Globe className="w-4 h-4" />
        </Button>

        {/* زر تبديل المظهر */}
        <Button
          variant="ghost"
          size="sm"
          onClick={onToggleDarkMode}
          className="h-9 w-9 p-0"
          title={isDarkMode ? "المظهر الفاتح" : "المظهر المظلم"}
        >
          {isDarkMode ? (
            <Sun className="w-4 h-4" />
          ) : (
            <Moon className="w-4 h-4" />
          )}
        </Button>

        {/* زر الإشعارات */}
        <Button
          variant="ghost"
          size="sm"
          className="h-9 w-9 p-0 relative"
          title="الإشعارات"
        >
          <Bell className="w-4 h-4" />
          <Badge
            variant="destructive"
            className="absolute -top-1 -right-1 h-5 w-5 p-0 text-xs flex items-center justify-center"
          >
            3
          </Badge>
        </Button>
      </div>
    </header>
  );
};

export default Header;