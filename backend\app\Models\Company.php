<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class Company extends Model
{
    protected $fillable = [
        'name',
        'name_en',
        'email',
        'phone',
        'tax_number',
        'commercial_register',
        'address',
        'city',
        'country',
        'logo',
        'subscription_plan',
        'subscription_status',
        'branches_count',
        'max_branches',
        'monthly_fee',
        'subscription_starts_at',
        'subscription_expires_at',
        'trial_ends_at',
        'settings',
        'is_active'
    ];

    protected $casts = [
        'settings' => 'array',
        'subscription_starts_at' => 'date',
        'subscription_expires_at' => 'date',
        'trial_ends_at' => 'date',
        'monthly_fee' => 'decimal:2',
        'is_active' => 'boolean'
    ];

    // العلاقات
    public function users(): HasMany
    {
        return $this->hasMany(User::class);
    }

    public function branches(): HasMany
    {
        return $this->hasMany(Branch::class);
    }

    public function products(): HasMany
    {
        return $this->hasMany(Product::class);
    }

    public function categories(): HasMany
    {
        return $this->hasMany(Category::class);
    }

    public function customers(): HasMany
    {
        return $this->hasMany(Customer::class);
    }

    public function suppliers(): HasMany
    {
        return $this->hasMany(Supplier::class);
    }

    public function sales(): HasMany
    {
        return $this->hasMany(Sale::class);
    }

    public function subscriptions(): HasMany
    {
        return $this->hasMany(Subscription::class);
    }

    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class);
    }

    public function currentSubscription(): HasOne
    {
        return $this->hasOne(Subscription::class)->where('status', 'active')->latest();
    }

    // الدوال المساعدة
    public function isActive(): bool
    {
        return $this->is_active && $this->subscription_status === 'active';
    }

    public function isTrialExpired(): bool
    {
        return $this->trial_ends_at && $this->trial_ends_at->isPast();
    }

    public function canAddBranch(): bool
    {
        return $this->branches_count < $this->max_branches;
    }

    public function getRemainingTrialDays(): int
    {
        if (!$this->trial_ends_at) {
            return 0;
        }

        return max(0, now()->diffInDays($this->trial_ends_at, false));
    }
}
