import React from 'react';
import { 
  LayoutDashboard, 
  ShoppingCart, 
  Package, 
  BarChart3, 
  Users, 
  Truck, 
  Settings, 
  LogOut,
  Store,
  Menu,
  X
} from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface SidebarProps {
  isCollapsed: boolean;
  onToggle: () => void;
  currentPath: string;
  onNavigate: (path: string) => void;
}

const Sidebar: React.FC<SidebarProps> = ({ 
  isCollapsed, 
  onToggle, 
  currentPath, 
  onNavigate 
}) => {
  const { t, isRTL } = useLanguage();
  const { user, company, logout } = useAuth();

  // عناصر القائمة الجانبية
  const menuItems = [
    {
      id: 'dashboard',
      label: t('nav.dashboard'),
      icon: LayoutDashboard,
      path: '/dashboard',
      permissions: ['read_dashboard']
    },
    {
      id: 'pos',
      label: t('nav.pos'),
      icon: ShoppingCart,
      path: '/pos',
      permissions: ['read_sales', 'write_sales']
    },
    {
      id: 'inventory',
      label: t('nav.inventory'),
      icon: Package,
      path: '/inventory',
      permissions: ['read_inventory']
    },
    {
      id: 'reports',
      label: t('nav.reports'),
      icon: BarChart3,
      path: '/reports',
      permissions: ['read_reports']
    },
    {
      id: 'customers',
      label: t('nav.customers'),
      icon: Users,
      path: '/customers',
      permissions: ['read_customers']
    },
    {
      id: 'suppliers',
      label: t('nav.suppliers'),
      icon: Truck,
      path: '/suppliers',
      permissions: ['read_suppliers']
    },
    {
      id: 'settings',
      label: t('nav.settings'),
      icon: Settings,
      path: '/settings',
      permissions: ['read_settings']
    }
  ];

  return (
    <div className={cn(
      "h-screen bg-card border-border shadow-medium transition-all duration-300 flex flex-col",
      isCollapsed ? "w-16" : "w-64",
      isRTL ? "border-l" : "border-r"
    )}>
      {/* رأس الشريط الجانبي */}
      <div className="p-4 border-b border-border">
        <div className="flex items-center justify-between">
          {!isCollapsed && (
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center">
                <Store className="w-5 h-5 text-primary-foreground" />
              </div>
              <div className="flex flex-col">
                <h1 className="font-bold text-lg font-arabic">
                  {company?.name || 'نظام POS'}
                </h1>
                <p className="text-xs text-muted-foreground">
                  {user?.role === 'admin' ? 'مدير عام' : 'المالك'}
                </p>
              </div>
            </div>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={onToggle}
            className="h-8 w-8 p-0"
          >
            {isCollapsed ? <Menu className="w-4 h-4" /> : <X className="w-4 h-4" />}
          </Button>
        </div>
      </div>

      {/* قائمة التنقل */}
      <nav className="flex-1 p-2 overflow-y-auto">
        <div className="space-y-1">
          {menuItems.map((item) => {
            const Icon = item.icon;
            const isActive = currentPath === item.path;
            
            return (
              <button
                key={item.id}
                onClick={() => onNavigate(item.path)}
                className={cn(
                  "sidebar-item w-full text-start font-arabic",
                  isActive && "active",
                  isCollapsed && "justify-center px-2"
                )}
                title={isCollapsed ? item.label : undefined}
              >
                <Icon className="w-5 h-5 flex-shrink-0" />
                {!isCollapsed && (
                  <span className="font-medium">{item.label}</span>
                )}
              </button>
            );
          })}
        </div>
      </nav>

      {/* معلومات المستخدم وتسجيل الخروج */}
      <div className="p-2 border-t border-border">
        {!isCollapsed && user && (
          <div className="p-3 bg-muted/50 rounded-lg mb-2">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                <span className="text-primary-foreground text-sm font-bold">
                  {user.name.charAt(0)}
                </span>
              </div>
              <div className="flex-1 min-w-0">
                <p className="font-medium truncate font-arabic">{user.name}</p>
                <p className="text-xs text-muted-foreground truncate">
                  {user.email}
                </p>
              </div>
            </div>
          </div>
        )}
        
        <Button
          variant="ghost"
          onClick={logout}
          className={cn(
            "w-full justify-start text-destructive hover:text-destructive hover:bg-destructive/10 font-arabic",
            isCollapsed && "justify-center px-2"
          )}
          title={isCollapsed ? t('nav.logout') : undefined}
        >
          <LogOut className="w-5 h-5 flex-shrink-0" />
          {!isCollapsed && (
            <span className="font-medium">{t('nav.logout')}</span>
          )}
        </Button>
      </div>
    </div>
  );
};

export default Sidebar;