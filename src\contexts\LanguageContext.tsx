import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

// أنواع اللغات المدعومة
type Language = 'ar' | 'en';

// واجهة سياق اللغة
interface LanguageContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (key: string) => string;
  isRTL: boolean;
}

// النصوص المترجمة
const translations = {
  ar: {
    // النصوص العامة
    'app.title': 'نظام نقاط البيع المتكامل',
    'app.description': 'حلول متقدمة لإدارة المبيعات والمخزون',
    
    // نصوص التنقل
    'nav.dashboard': 'لوحة التحكم',
    'nav.pos': 'نقطة البيع',
    'nav.inventory': 'المخزون',
    'nav.reports': 'التقارير',
    'nav.customers': 'العملاء',
    'nav.suppliers': 'الموردين',
    'nav.settings': 'الإعدادات',
    'nav.logout': 'تسجيل الخروج',
    
    // نصوص تسجيل الدخول
    'login.title': 'تسجيل الدخول',
    'login.email': 'البريد الإلكتروني',
    'login.password': 'كلمة المرور',
    'login.submit': 'دخول',
    'login.loading': 'جاري تسجيل الدخول...',
    'login.forgot': 'هل نسيت كلمة المرور؟',
    
    // نصوص لوحة التحكم
    'dashboard.welcome': 'مرحباً بك',
    'dashboard.sales.today': 'مبيعات اليوم',
    'dashboard.sales.month': 'مبيعات الشهر',
    'dashboard.products.count': 'عدد المنتجات',
    'dashboard.customers.count': 'عدد العملاء',
    
    // نصوص نقطة البيع
    'pos.title': 'نقطة البيع',
    'pos.search': 'ابحث عن منتج...',
    'pos.cart': 'سلة المشتريات',
    'pos.total': 'المجموع',
    'pos.payment': 'الدفع',
    'pos.print': 'طباعة الفاتورة',
    
    // الأزرار والإجراءات
    'btn.save': 'حفظ',
    'btn.cancel': 'إلغاء',
    'btn.edit': 'تحرير',
    'btn.delete': 'حذف',
    'btn.add': 'إضافة',
    'btn.search': 'بحث',
    'btn.filter': 'تصفية',
    'btn.export': 'تصدير',
    
    // الرسائل
    'msg.success': 'تم بنجاح',
    'msg.error': 'حدث خطأ',
    'msg.loading': 'جاري التحميل...',
    'msg.nodata': 'لا توجد بيانات',
    
    // نصوص الوقت
    'time.today': 'اليوم',
    'time.yesterday': 'أمس',
    'time.week': 'هذا الأسبوع',
    'time.month': 'هذا الشهر',
    'time.year': 'هذا العام'
  },
  en: {
    // General texts
    'app.title': 'Integrated POS System',
    'app.description': 'Advanced solutions for sales and inventory management',
    
    // Navigation texts
    'nav.dashboard': 'Dashboard',
    'nav.pos': 'Point of Sale',
    'nav.inventory': 'Inventory',
    'nav.reports': 'Reports',
    'nav.customers': 'Customers',
    'nav.suppliers': 'Suppliers',
    'nav.settings': 'Settings',
    'nav.logout': 'Logout',
    
    // Login texts
    'login.title': 'Login',
    'login.email': 'Email',
    'login.password': 'Password',
    'login.submit': 'Login',
    'login.loading': 'Logging in...',
    'login.forgot': 'Forgot Password?',
    
    // Dashboard texts
    'dashboard.welcome': 'Welcome',
    'dashboard.sales.today': 'Today\'s Sales',
    'dashboard.sales.month': 'Month\'s Sales',
    'dashboard.products.count': 'Products Count',
    'dashboard.customers.count': 'Customers Count',
    
    // POS texts
    'pos.title': 'Point of Sale',
    'pos.search': 'Search for product...',
    'pos.cart': 'Shopping Cart',
    'pos.total': 'Total',
    'pos.payment': 'Payment',
    'pos.print': 'Print Receipt',
    
    // Buttons and actions
    'btn.save': 'Save',
    'btn.cancel': 'Cancel',
    'btn.edit': 'Edit',
    'btn.delete': 'Delete',
    'btn.add': 'Add',
    'btn.search': 'Search',
    'btn.filter': 'Filter',
    'btn.export': 'Export',
    
    // Messages
    'msg.success': 'Success',
    'msg.error': 'Error occurred',
    'msg.loading': 'Loading...',
    'msg.nodata': 'No data available',
    
    // Time texts
    'time.today': 'Today',
    'time.yesterday': 'Yesterday',
    'time.week': 'This Week',
    'time.month': 'This Month',
    'time.year': 'This Year'
  }
};

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

// مزود سياق اللغة
export const LanguageProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [language, setLanguage] = useState<Language>('ar');

  // دالة الترجمة
  const t = (key: string): string => {
    return translations[language][key as keyof typeof translations[typeof language]] || key;
  };

  // تحديث اتجاه الصفحة عند تغيير اللغة
  useEffect(() => {
    const direction = language === 'ar' ? 'rtl' : 'ltr';
    document.documentElement.setAttribute('dir', direction);
    document.documentElement.setAttribute('lang', language);
  }, [language]);

  const value: LanguageContextType = {
    language,
    setLanguage,
    t,
    isRTL: language === 'ar'
  };

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
};

// خطاف استخدام سياق اللغة
export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};