<?php declare(strict_types=1);
/*
 * This file is part of PHPUnit.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace PHPUnit\Metadata;

use PHPUnit\Runner\Extension\Extension;

/**
 * @immutable
 *
 * @no-named-arguments Parameter names are not covered by the backward compatibility promise for PHPUnit
 */
final readonly class RequiresPhpunitExtension extends Metadata
{
    /**
     * @var class-string<Extension>
     */
    private string $extensionClass;

    /**
     * @param class-string<Extension> $extensionClass
     */
    public function __construct(int $level, string $extensionClass)
    {
        parent::__construct($level);

        $this->extensionClass = $extensionClass;
    }

    public function isRequiresPhpunitExtension(): true
    {
        return true;
    }

    /**
     * @return class-string<Extension>
     */
    public function extensionClass(): string
    {
        return $this->extensionClass;
    }
}
