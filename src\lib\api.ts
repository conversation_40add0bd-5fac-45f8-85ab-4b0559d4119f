// API Configuration and Services
const API_BASE_URL = 'http://localhost:8000/api/v1';

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  message?: string;
  data?: T;
  errors?: Record<string, string[]>;
}

export interface User {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'supervisor' | 'owner' | 'manager' | 'cashier';
  permissions: string[];
  company?: {
    id: string;
    name: string;
    subscription_plan: string;
    subscription_status: string;
  };
  branch?: {
    id: string;
    name: string;
  };
}

export interface LoginResponse {
  user: User;
  token: string;
  token_type: string;
}

export interface Product {
  id: string;
  name: string;
  name_en?: string;
  sku: string;
  barcode?: string;
  selling_price: number;
  cost_price: number;
  stock_quantity: number;
  min_stock_level: number;
  unit: string;
  image?: string;
  category?: {
    id: string;
    name: string;
    color: string;
  };
  is_active: boolean;
}

export interface Sale {
  id: string;
  invoice_number: string;
  customer_name?: string;
  total_amount: number;
  payment_method: string;
  status: string;
  cashier: string;
  created_at: string;
}

export interface DashboardStats {
  today_sales: {
    amount: string;
    count: number;
    change: string;
  };
  month_sales: {
    amount: string;
    change: string;
  };
  products_count: number;
  customers_count: number;
  low_stock_count: number;
}

// API Client Class
class ApiClient {
  private baseURL: string;
  private token: string | null = null;

  constructor(baseURL: string) {
    this.baseURL = baseURL;
    this.token = localStorage.getItem('auth_token');
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseURL}${endpoint}`;
    
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      ...options.headers,
    };

    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`;
    }

    try {
      const response = await fetch(url, {
        ...options,
        headers,
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'حدث خطأ في الطلب');
      }

      return data;
    } catch (error) {
      console.error('API Error:', error);
      throw error;
    }
  }

  // Authentication Methods
  async login(email: string, password: string): Promise<ApiResponse<LoginResponse>> {
    const response = await this.request<LoginResponse>('/login', {
      method: 'POST',
      body: JSON.stringify({ email, password }),
    });

    if (response.success && response.data?.token) {
      this.setToken(response.data.token);
    }

    return response;
  }

  async logout(): Promise<ApiResponse> {
    const response = await this.request('/logout', {
      method: 'POST',
    });

    if (response.success) {
      this.clearToken();
    }

    return response;
  }

  async getMe(): Promise<ApiResponse<{ user: User }>> {
    return this.request<{ user: User }>('/me');
  }

  async updatePassword(
    currentPassword: string,
    newPassword: string,
    newPasswordConfirmation: string
  ): Promise<ApiResponse> {
    return this.request('/password', {
      method: 'PUT',
      body: JSON.stringify({
        current_password: currentPassword,
        new_password: newPassword,
        new_password_confirmation: newPasswordConfirmation,
      }),
    });
  }

  // Dashboard Methods
  async getDashboardStats(): Promise<ApiResponse<DashboardStats>> {
    return this.request<DashboardStats>('/dashboard/stats');
  }

  async getRecentSales(): Promise<ApiResponse<Sale[]>> {
    return this.request<Sale[]>('/dashboard/recent-sales');
  }

  async getTopProducts(): Promise<ApiResponse<any[]>> {
    return this.request<any[]>('/dashboard/top-products');
  }

  async getSalesChart(period: string = 'week'): Promise<ApiResponse<any[]>> {
    return this.request<any[]>(`/dashboard/sales-chart?period=${period}`);
  }

  // Products Methods
  async getProducts(page: number = 1, search?: string): Promise<ApiResponse<{
    data: Product[];
    total: number;
    per_page: number;
    current_page: number;
  }>> {
    const params = new URLSearchParams({
      page: page.toString(),
    });
    
    if (search) {
      params.append('search', search);
    }

    return this.request<any>(`/products?${params.toString()}`);
  }

  async getProduct(id: string): Promise<ApiResponse<Product>> {
    return this.request<Product>(`/products/${id}`);
  }

  async createProduct(product: Partial<Product>): Promise<ApiResponse<Product>> {
    return this.request<Product>('/products', {
      method: 'POST',
      body: JSON.stringify(product),
    });
  }

  async updateProduct(id: string, product: Partial<Product>): Promise<ApiResponse<Product>> {
    return this.request<Product>(`/products/${id}`, {
      method: 'PUT',
      body: JSON.stringify(product),
    });
  }

  async deleteProduct(id: string): Promise<ApiResponse> {
    return this.request(`/products/${id}`, {
      method: 'DELETE',
    });
  }

  async searchProducts(term: string): Promise<ApiResponse<Product[]>> {
    return this.request<Product[]>(`/products/search/${encodeURIComponent(term)}`);
  }

  async findProductByBarcode(barcode: string): Promise<ApiResponse<Product>> {
    return this.request<Product>(`/products/barcode/${encodeURIComponent(barcode)}`);
  }

  async getLowStockProducts(): Promise<ApiResponse<Product[]>> {
    return this.request<Product[]>('/products/low-stock');
  }

  // Sales Methods
  async createPOSSale(saleData: any): Promise<ApiResponse<Sale>> {
    return this.request<Sale>('/sales/pos', {
      method: 'POST',
      body: JSON.stringify(saleData),
    });
  }

  async getSales(page: number = 1): Promise<ApiResponse<{
    data: Sale[];
    total: number;
    per_page: number;
    current_page: number;
  }>> {
    return this.request<any>(`/sales?page=${page}`);
  }

  async getSale(id: string): Promise<ApiResponse<Sale>> {
    return this.request<Sale>(`/sales/${id}`);
  }

  async refundSale(id: string, reason?: string): Promise<ApiResponse> {
    return this.request(`/sales/${id}/refund`, {
      method: 'POST',
      body: JSON.stringify({ reason }),
    });
  }

  async getSaleReceipt(id: string): Promise<ApiResponse<any>> {
    return this.request<any>(`/sales/${id}/receipt`);
  }

  // Token Management
  setToken(token: string): void {
    this.token = token;
    localStorage.setItem('auth_token', token);
  }

  clearToken(): void {
    this.token = null;
    localStorage.removeItem('auth_token');
  }

  getToken(): string | null {
    return this.token;
  }

  isAuthenticated(): boolean {
    return !!this.token;
  }
}

// Create and export API client instance
export const apiClient = new ApiClient(API_BASE_URL);

// Export individual API functions for convenience
export const authAPI = {
  login: (email: string, password: string) => apiClient.login(email, password),
  logout: () => apiClient.logout(),
  getMe: () => apiClient.getMe(),
  updatePassword: (current: string, newPass: string, confirm: string) => 
    apiClient.updatePassword(current, newPass, confirm),
};

export const dashboardAPI = {
  getStats: () => apiClient.getDashboardStats(),
  getRecentSales: () => apiClient.getRecentSales(),
  getTopProducts: () => apiClient.getTopProducts(),
  getSalesChart: (period?: string) => apiClient.getSalesChart(period),
};

export const productsAPI = {
  getAll: (page?: number, search?: string) => apiClient.getProducts(page, search),
  getById: (id: string) => apiClient.getProduct(id),
  create: (product: Partial<Product>) => apiClient.createProduct(product),
  update: (id: string, product: Partial<Product>) => apiClient.updateProduct(id, product),
  delete: (id: string) => apiClient.deleteProduct(id),
  search: (term: string) => apiClient.searchProducts(term),
  findByBarcode: (barcode: string) => apiClient.findProductByBarcode(barcode),
  getLowStock: () => apiClient.getLowStockProducts(),
};

export const salesAPI = {
  createPOS: (saleData: any) => apiClient.createPOSSale(saleData),
  getAll: (page?: number) => apiClient.getSales(page),
  getById: (id: string) => apiClient.getSale(id),
  refund: (id: string, reason?: string) => apiClient.refundSale(id, reason),
  getReceipt: (id: string) => apiClient.getSaleReceipt(id),
};
