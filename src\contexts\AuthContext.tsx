import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { authAPI, User as ApiUser, ApiResponse, LoginResponse } from '@/lib/api';

// نوع بيانات المستخدم المحلي
interface User {
  id: string;
  email: string;
  name: string;
  role: 'admin' | 'supervisor' | 'owner' | 'manager' | 'cashier';
  companyId?: string;
  branchId?: string;
  permissions: string[];
  avatar?: string;
}

// نوع بيانات الشركة
interface Company {
  id: string;
  name: string;
  subscriptionPlan: 'basic' | 'premium' | 'enterprise';
  subscriptionStatus: 'active' | 'inactive' | 'trial';
  branchesCount: number;
  maxBranches: number;
  expiresAt: string;
}

// سياق المصادقة
interface AuthContextType {
  user: User | null;
  company: Company | null;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  isAuthenticated: boolean;
  hasPermission: (permission: string) => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// مزود سياق المصادقة
export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [company, setCompany] = useState<Company | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // تسجيل الدخول باستخدام API
  const login = async (email: string, password: string) => {
    setIsLoading(true);
    try {
      const response = await authAPI.login(email, password);

      if (response.success && response.data) {
        const userData = response.data.user;

        // تحويل بيانات المستخدم للتنسيق المطلوب
        const user: User = {
          id: userData.id,
          email: userData.email,
          name: userData.name,
          role: userData.role,
          permissions: userData.permissions,
          companyId: userData.company?.id,
          branchId: userData.branch?.id,
          avatar: '/placeholder.svg'
        };

        // تحويل بيانات الشركة للتنسيق المطلوب
        const company: Company | null = userData.company ? {
          id: userData.company.id,
          name: userData.company.name,
          subscriptionPlan: userData.company.subscription_plan as 'basic' | 'premium' | 'enterprise',
          subscriptionStatus: userData.company.subscription_status as 'active' | 'inactive' | 'trial',
          branchesCount: 2, // سيتم جلبها من API لاحقاً
          maxBranches: 5, // سيتم جلبها من API لاحقاً
          expiresAt: '2024-12-31' // سيتم جلبها من API لاحقاً
        } : null;

        setUser(user);
        setCompany(company);
      } else {
        throw new Error(response.message || 'فشل في تسجيل الدخول');
      }
    } catch (error: any) {
      throw new Error(error.message || 'فشل في تسجيل الدخول');
    } finally {
      setIsLoading(false);
    }
  };

  // تسجيل الخروج
  const logout = async () => {
    try {
      await authAPI.logout();
    } catch (error) {
      console.error('خطأ في تسجيل الخروج:', error);
    } finally {
      setUser(null);
      setCompany(null);
    }
  };

  // التحقق من الصلاحيات
  const hasPermission = (permission: string): boolean => {
    return user?.permissions.includes(permission) || false;
  };

  // التحقق من المصادقة عند تحميل التطبيق
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const token = localStorage.getItem('auth_token');
        if (token) {
          const response = await authAPI.getMe();
          if (response.success && response.data) {
            const userData = response.data.user;

            const user: User = {
              id: userData.id,
              email: userData.email,
              name: userData.name,
              role: userData.role,
              permissions: userData.permissions,
              companyId: userData.company?.id,
              branchId: userData.branch?.id,
              avatar: '/placeholder.svg'
            };

            const company: Company | null = userData.company ? {
              id: userData.company.id,
              name: userData.company.name,
              subscriptionPlan: userData.company.subscription_plan as 'basic' | 'premium' | 'enterprise',
              subscriptionStatus: userData.company.subscription_status as 'active' | 'inactive' | 'trial',
              branchesCount: 2,
              maxBranches: 5,
              expiresAt: '2024-12-31'
            } : null;

            setUser(user);
            setCompany(company);
          }
        }
      } catch (error) {
        console.error('خطأ في التحقق من المصادقة:', error);
        localStorage.removeItem('auth_token');
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, []);

  const value: AuthContextType = {
    user,
    company,
    isLoading,
    login,
    logout,
    isAuthenticated: !!user,
    hasPermission
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// خطاف استخدام سياق المصادقة
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};