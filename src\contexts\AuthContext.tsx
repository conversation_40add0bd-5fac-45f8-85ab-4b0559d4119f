import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

// نوع بيانات المستخدم
interface User {
  id: string;
  email: string;
  name: string;
  role: 'admin' | 'supervisor' | 'owner' | 'manager' | 'cashier';
  companyId?: string;
  branchId?: string;
  permissions: string[];
  avatar?: string;
}

// نوع بيانات الشركة
interface Company {
  id: string;
  name: string;
  subscriptionPlan: 'basic' | 'premium' | 'enterprise';
  subscriptionStatus: 'active' | 'inactive' | 'trial';
  branchesCount: number;
  maxBranches: number;
  expiresAt: string;
}

// سياق المصادقة
interface AuthContextType {
  user: User | null;
  company: Company | null;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  isAuthenticated: boolean;
  hasPermission: (permission: string) => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// مزود سياق المصادقة
export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [company, setCompany] = useState<Company | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // محاكاة تسجيل الدخول
  const login = async (email: string, password: string) => {
    setIsLoading(true);
    try {
      // محاكاة استدعاء API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // بيانات تجريبية للمستخدم
      const mockUser: User = {
        id: '1',
        email,
        name: 'أحمد محمد',
        role: email.includes('admin') ? 'admin' : 'owner',
        companyId: '1',
        branchId: '1',
        permissions: ['read_sales', 'write_sales', 'read_inventory', 'write_inventory'],
        avatar: '/placeholder.svg'
      };

      const mockCompany: Company = {
        id: '1',
        name: 'متجر الأماني التجاري',
        subscriptionPlan: 'premium',
        subscriptionStatus: 'active',
        branchesCount: 2,
        maxBranches: 5,
        expiresAt: '2024-12-31'
      };

      setUser(mockUser);
      setCompany(mockCompany);
    } catch (error) {
      throw new Error('فشل في تسجيل الدخول');
    } finally {
      setIsLoading(false);
    }
  };

  // تسجيل الخروج
  const logout = () => {
    setUser(null);
    setCompany(null);
  };

  // التحقق من الصلاحيات
  const hasPermission = (permission: string): boolean => {
    return user?.permissions.includes(permission) || false;
  };

  // التحقق من المصادقة عند تحميل التطبيق
  useEffect(() => {
    const checkAuth = async () => {
      // محاكاة التحقق من token المحفوظ
      await new Promise(resolve => setTimeout(resolve, 500));
      setIsLoading(false);
    };

    checkAuth();
  }, []);

  const value: AuthContextType = {
    user,
    company,
    isLoading,
    login,
    logout,
    isAuthenticated: !!user,
    hasPermission
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// خطاف استخدام سياق المصادقة
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};