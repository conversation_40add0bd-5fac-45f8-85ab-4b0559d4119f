import React, { useState, useEffect } from 'react';
import { Toaster } from '@/components/ui/toaster';
import { useAuth } from '@/contexts/AuthContext';
import Sidebar from './Sidebar';
import Header from './Header';

interface LayoutProps {
  children: React.ReactNode;
  title: string;
  currentPath: string;
  onNavigate: (path: string) => void;
}

const Layout: React.FC<LayoutProps> = ({ 
  children, 
  title, 
  currentPath, 
  onNavigate 
}) => {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isDarkMode, setIsDarkMode] = useState(false);
  const { isAuthenticated } = useAuth();

  // تبديل الشريط الجانبي
  const toggleSidebar = () => {
    setIsCollapsed(!isCollapsed);
  };

  // تبديل المظهر المظلم/الفاتح
  const toggleDarkMode = () => {
    setIsDarkMode(!isDarkMode);
    document.documentElement.classList.toggle('dark');
  };

  // تحديد ما إذا كان الشريط الجانبي مطوياً تلقائياً في الشاشات الصغيرة
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 768) {
        setIsCollapsed(true);
      }
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // إذا لم يكن المستخدم مسجلاً دخولاً، لا نعرض التخطيط
  if (!isAuthenticated) {
    return <>{children}</>;
  }

  return (
    <div className="h-screen flex bg-background">
      {/* الشريط الجانبي */}
      <Sidebar
        isCollapsed={isCollapsed}
        onToggle={toggleSidebar}
        currentPath={currentPath}
        onNavigate={onNavigate}
      />

      {/* المحتوى الرئيسي */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* رأس الصفحة */}
        <Header
          title={title}
          isDarkMode={isDarkMode}
          onToggleDarkMode={toggleDarkMode}
        />

        {/* منطقة المحتوى */}
        <main className="flex-1 overflow-y-auto bg-background p-6">
          <div className="animate-fade-in">
            {children}
          </div>
        </main>
      </div>

      {/* مكون الإشعارات */}
      <Toaster />
    </div>
  );
};

export default Layout;