import React from 'react';
import { 
  TrendingUp, 
  TrendingDown, 
  ShoppingCart, 
  Package, 
  Users, 
  DollarSign,
  Calendar,
  BarChart3,
  Pie<PERSON><PERSON>,
  Clock
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useLanguage } from '@/contexts/LanguageContext';
import { useAuth } from '@/contexts/AuthContext';

const Dashboard: React.FC = () => {
  const { t } = useLanguage();
  const { user, company } = useAuth();

  // بيانات إحصائية تجريبية
  const stats = [
    {
      title: t('dashboard.sales.today'),
      value: '24,750',
      currency: 'ر.س',
      change: '+12.5%',
      changeType: 'increase' as const,
      icon: DollarSign,
      description: 'مقارنة بالأمس'
    },
    {
      title: 'عدد الطلبات',
      value: '156',
      change: '+8.2%',
      changeType: 'increase' as const,
      icon: ShoppingCart,
      description: 'طلب اليوم'
    },
    {
      title: 'المنتجات المتاحة',
      value: '1,247',
      change: '-2.1%',
      changeType: 'decrease' as const,
      icon: Package,
      description: 'في المخزون'
    },
    {
      title: 'العملاء النشطين',
      value: '892',
      change: '+15.3%',
      changeType: 'increase' as const,
      icon: Users,
      description: 'هذا الشهر'
    }
  ];

  // المبيعات الأخيرة
  const recentSales = [
    {
      id: '1',
      customer: 'أحمد محمد الأحمد',
      amount: 1250,
      items: 5,
      time: '10:30 ص',
      status: 'مكتملة'
    },
    {
      id: '2',
      customer: 'فاطمة علي السالم',
      amount: 890,
      items: 3,
      time: '10:15 ص',
      status: 'مكتملة'
    },
    {
      id: '3',
      customer: 'محمد سعد الكريم',
      amount: 2150,
      items: 8,
      time: '09:45 ص',
      status: 'مكتملة'
    },
    {
      id: '4',
      customer: 'نورا عبدالله القحطاني',
      amount: 675,
      items: 2,
      time: '09:30 ص',
      status: 'معلقة'
    }
  ];

  // المنتجات الأكثر مبيعاً
  const topProducts = [
    { name: 'جهاز آيفون 15 برو', sales: 45, revenue: 67500 },
    { name: 'سماعات AirPods Pro', sales: 78, revenue: 23400 },
    { name: 'ساعة آبل Series 9', sales: 32, revenue: 19200 },
    { name: 'جهاز iPad Air', sales: 28, revenue: 16800 },
    { name: 'كيبورد لوجيتك MX', sales: 56, revenue: 8400 }
  ];

  return (
    <div className="space-y-6">
      {/* ترحيب شخصي */}
      <div className="bg-gradient-primary p-6 rounded-xl text-primary-foreground">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold font-arabic">
              {t('dashboard.welcome')} {user?.name}
            </h1>
            <p className="text-primary-foreground/80 font-arabic mt-1">
              إليك ملخص أداء متجرك اليوم
            </p>
          </div>
          <div className="text-right">
            <p className="text-primary-foreground/80 text-sm">
              {new Date().toLocaleDateString('ar-SA', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              })}
            </p>
            <div className="flex items-center gap-2 mt-1">
              <Clock className="w-4 h-4" />
              <span className="text-sm">
                {new Date().toLocaleTimeString('ar-SA', {
                  hour: '2-digit',
                  minute: '2-digit'
                })}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* الإحصائيات الرئيسية */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <Card key={index} className="card-interactive">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium font-arabic">
                  {stat.title}
                </CardTitle>
                <Icon className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold font-english">
                  {stat.value}
                  {stat.currency && (
                    <span className="text-sm text-muted-foreground font-arabic mr-1">
                      {stat.currency}
                    </span>
                  )}
                </div>
                <div className="flex items-center gap-1 text-xs text-muted-foreground mt-1">
                  {stat.changeType === 'increase' ? (
                    <TrendingUp className="w-3 h-3 text-success" />
                  ) : (
                    <TrendingDown className="w-3 h-3 text-destructive" />
                  )}
                  <span className={
                    stat.changeType === 'increase' ? 'text-success' : 'text-destructive'
                  }>
                    {stat.change}
                  </span>
                  <span className="font-arabic">{stat.description}</span>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* الصف الثاني من البطاقات */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* المبيعات الأخيرة */}
        <Card className="card-elevated">
          <CardHeader>
            <CardTitle className="font-arabic">آخر المبيعات</CardTitle>
            <CardDescription className="font-arabic">
              أحدث المعاملات المالية اليوم
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentSales.map((sale) => (
                <div key={sale.id} className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                  <div className="flex-1">
                    <p className="font-medium font-arabic">{sale.customer}</p>
                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                      <span className="font-arabic">{sale.items} منتجات</span>
                      <span>{sale.time}</span>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-bold font-english">
                      {sale.amount.toLocaleString()} ر.س
                    </p>
                    <Badge 
                      variant={sale.status === 'مكتملة' ? 'default' : 'secondary'}
                      className="text-xs font-arabic"
                    >
                      {sale.status}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
            <Button variant="outline" className="w-full mt-4 font-arabic">
              عرض جميع المبيعات
            </Button>
          </CardContent>
        </Card>

        {/* المنتجات الأكثر مبيعاً */}
        <Card className="card-elevated">
          <CardHeader>
            <CardTitle className="font-arabic">المنتجات الأكثر مبيعاً</CardTitle>
            <CardDescription className="font-arabic">
              أفضل المنتجات أداءً هذا الأسبوع
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {topProducts.map((product, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
                      <span className="text-primary font-bold text-sm">
                        {index + 1}
                      </span>
                    </div>
                    <div>
                      <p className="font-medium font-arabic text-sm">
                        {product.name}
                      </p>
                      <p className="text-xs text-muted-foreground font-arabic">
                        {product.sales} مبيعة
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-bold text-sm font-english">
                      {product.revenue.toLocaleString()} ر.س
                    </p>
                  </div>
                </div>
              ))}
            </div>
            <Button variant="outline" className="w-full mt-4 font-arabic">
              عرض تقرير المنتجات
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* إجراءات سريعة */}
      <Card className="card-elevated">
        <CardHeader>
          <CardTitle className="font-arabic">إجراءات سريعة</CardTitle>
          <CardDescription className="font-arabic">
            الأدوات الأكثر استخداماً في نظامك
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button className="h-20 flex-col gap-2 btn-gradient-primary">
              <ShoppingCart className="w-6 h-6" />
              <span className="font-arabic">نقطة البيع</span>
            </Button>
            <Button variant="outline" className="h-20 flex-col gap-2">
              <Package className="w-6 h-6" />
              <span className="font-arabic">إدارة المخزون</span>
            </Button>
            <Button variant="outline" className="h-20 flex-col gap-2">
              <BarChart3 className="w-6 h-6" />
              <span className="font-arabic">التقارير</span>
            </Button>
            <Button variant="outline" className="h-20 flex-col gap-2">
              <Users className="w-6 h-6" />
              <span className="font-arabic">العملاء</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Dashboard;