<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('companies', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('name_en')->nullable();
            $table->string('email')->unique();
            $table->string('phone')->nullable();
            $table->string('tax_number')->nullable();
            $table->string('commercial_register')->nullable();
            $table->text('address')->nullable();
            $table->string('city')->nullable();
            $table->string('country')->default('SA');
            $table->string('logo')->nullable();
            $table->enum('subscription_plan', ['basic', 'premium', 'enterprise'])->default('basic');
            $table->enum('subscription_status', ['active', 'inactive', 'trial', 'suspended'])->default('trial');
            $table->integer('branches_count')->default(1);
            $table->integer('max_branches')->default(1);
            $table->decimal('monthly_fee', 10, 2)->default(0);
            $table->date('subscription_starts_at')->nullable();
            $table->date('subscription_expires_at')->nullable();
            $table->date('trial_ends_at')->nullable();
            $table->json('settings')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('companies');
    }
};
