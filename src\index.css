@tailwind base;
@tailwind components;
@tailwind utilities;

/* نظام التصميم لنظام نقاط البيع المتكامل */
/* جميع الألوان يجب أن تكون HSL لدعم المظهر المظلم والفاتح */

@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap');

@layer base {
  :root {
    /* الألوان الأساسية للنظام */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    /* ألوان البطاقات والمحتوى */
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    /* الألوان الرئيسية - أخضر احترافي لنظام POS */
    --primary: 142 76% 36%;
    --primary-foreground: 0 0% 98%;
    --primary-glow: 142 76% 60%;
    
    /* الألوان الثانوية */
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    /* الألوان المكتومة */
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    /* ألوان التمييز - أزرق احترافي */
    --accent: 217 91% 60%;
    --accent-foreground: 0 0% 98%;

    /* ألوان التحذير والخطر */
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    
    /* ألوان النجاح */
    --success: 142 76% 36%;
    --success-foreground: 0 0% 98%;
    
    /* ألوان التحذير */
    --warning: 45 93% 47%;
    --warning-foreground: 0 0% 98%;

    /* الحدود والمدخلات */
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 142 76% 36%;

    /* متدرجات ألوان احترافية */
    --gradient-primary: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary-glow)));
    --gradient-accent: linear-gradient(135deg, hsl(var(--accent)), hsl(217 91% 70%));
    --gradient-success: linear-gradient(135deg, hsl(var(--success)), hsl(142 76% 50%));
    
    /* ظلال احترافية */
    --shadow-soft: 0 2px 8px hsl(var(--foreground) / 0.1);
    --shadow-medium: 0 4px 16px hsl(var(--foreground) / 0.15);
    --shadow-large: 0 8px 32px hsl(var(--foreground) / 0.2);
    --shadow-primary: 0 4px 16px hsl(var(--primary) / 0.3);

    /* الانتقالات السلسة */
    --transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);

    --radius: 0.75rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    /* الخلفية المظلمة */
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;

    /* البطاقات في المظهر المظلم */
    --card: 240 10% 8%;
    --card-foreground: 0 0% 95%;

    --popover: 240 10% 8%;
    --popover-foreground: 0 0% 95%;

    /* الألوان الرئيسية في المظهر المظلم */
    --primary: 142 76% 45%;
    --primary-foreground: 0 0% 98%;
    --primary-glow: 142 76% 65%;

    /* الألوان الثانوية */
    --secondary: 240 4% 16%;
    --secondary-foreground: 0 0% 90%;

    /* الألوان المكتومة */
    --muted: 240 4% 16%;
    --muted-foreground: 240 5% 65%;

    /* ألوان التمييز */
    --accent: 217 91% 65%;
    --accent-foreground: 0 0% 98%;

    /* ألوان التحذير والخطر */
    --destructive: 0 84% 65%;
    --destructive-foreground: 0 0% 98%;
    
    /* ألوان النجاح */
    --success: 142 76% 45%;
    --success-foreground: 0 0% 98%;
    
    /* ألوان التحذير */
    --warning: 45 93% 55%;
    --warning-foreground: 0 0% 98%;

    /* الحدود والمدخلات */
    --border: 240 4% 20%;
    --input: 240 4% 20%;
    --ring: 142 76% 45%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
    font-family: 'Cairo', 'Inter', system-ui, -apple-system, sans-serif;
  }
  
  /* دعم اللغة العربية و RTL */
  html[dir="rtl"] {
    direction: rtl;
  }
  
  html[dir="ltr"] {
    direction: ltr;
  }
  
  /* تحسينات للخطوط العربية */
  .font-arabic {
    font-family: 'Cairo', sans-serif;
  }
  
  .font-english {
    font-family: 'Inter', sans-serif;
  }
}

/* طبقة المكونات المخصصة */
@layer components {
  /* أزرار متدرجة احترافية */
  .btn-gradient-primary {
    @apply bg-gradient-to-r from-primary to-primary-glow text-primary-foreground 
           shadow-primary hover:shadow-lg transform hover:scale-105 
           transition-all duration-300;
  }
  
  .btn-gradient-accent {
    @apply bg-gradient-to-r from-accent to-blue-500 text-accent-foreground 
           shadow-medium hover:shadow-lg transform hover:scale-105 
           transition-all duration-300;
  }
  
  /* بطاقات مع ظلال مخصصة */
  .card-elevated {
    @apply bg-card border-border shadow-medium hover:shadow-large 
           transition-all duration-300;
  }
  
  .card-interactive {
    @apply card-elevated hover:scale-105 cursor-pointer;
  }
  
  /* شريط جانبي احترافي */
  .sidebar-item {
    @apply flex items-center gap-3 px-4 py-3 rounded-lg 
           transition-all duration-200 hover:bg-accent/10 
           hover:text-accent;
  }
  
  .sidebar-item.active {
    @apply bg-primary/10 text-primary border-r-2 border-primary;
  }
  
  /* مدخلات محسنة */
  .input-enhanced {
    @apply bg-background border-border focus:border-primary 
           focus:ring-2 focus:ring-primary/20 transition-all duration-200;
  }
  
  /* تأثيرات الحركة */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }
  
  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }
  
  .animate-bounce-subtle {
    animation: bounceSubtle 2s infinite;
  }
}

/* إطارات الحركة المخصصة */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideUp {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes bounceSubtle {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-5px); }
  60% { transform: translateY(-3px); }
}

/* تحسينات للجوال */
@media (max-width: 768px) {
  .mobile-optimized {
    @apply text-sm p-3;
  }
  
  .mobile-card {
    @apply mx-2 rounded-xl;
  }
}