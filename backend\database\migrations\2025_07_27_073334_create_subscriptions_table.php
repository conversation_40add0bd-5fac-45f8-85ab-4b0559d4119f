<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('subscriptions', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('company_id');
            $table->string('plan_name');
            $table->enum('plan_type', ['basic', 'premium', 'enterprise']);
            $table->decimal('amount', 10, 2);
            $table->string('currency', 3)->default('SAR');
            $table->integer('billing_cycle_days')->default(30);
            $table->date('starts_at');
            $table->date('expires_at');
            $table->enum('status', ['active', 'inactive', 'cancelled', 'expired'])->default('active');
            $table->string('tap_subscription_id')->nullable();
            $table->json('features')->nullable();
            $table->text('notes')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subscriptions');
    }
};
